package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.good.base.ConstFashionVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   
   public class MallConfig
   {
      
      private static var _instance:MallConfig;
      
      private var _news:Array;
      
      private var _goods:Array;
      
      private var _bags:Array;
      
      private var _skins:Array;
      
      public function MallConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MallConfig
      {
         if(!_instance)
         {
            _instance = new MallConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._news = [];
         // 基础货币类物品
         this._news[this._news.length] = new MallMoneyVO(10000,1,new BaseRewardVO(10000,1));
         this._news[this._news.length] = new MallMoneyVO(10001,1,new BaseRewardVO(10001,1));
         this._news[this._news.length] = new MallMoneyVO(10002,1,new BaseRewardVO(10002,1));
         this._news[this._news.length] = new MallMoneyVO(10003,1,new BaseRewardVO(10003,1));
         this._news[this._news.length] = new MallMoneyVO(10004,1,new BaseRewardVO(10004,1));
         this._news[this._news.length] = new MallMoneyVO(10005,1,new BaseRewardVO(10005,1));
         this._news[this._news.length] = new MallMoneyVO(10006,1,new BaseRewardVO(10006,1));
         this._news[this._news.length] = new MallMoneyVO(10007,1,new BaseRewardVO(10007,1));
         this._news[this._news.length] = new MallMoneyVO(10008,1,new BaseRewardVO(10008,1));
         this._news[this._news.length] = new MallMoneyVO(10009,1,new BaseRewardVO(10009,1));
         this._news[this._news.length] = new MallMoneyVO(10010,1,new BaseRewardVO(10010,1));
         this._news[this._news.length] = new MallMoneyVO(10014,1,new BaseRewardVO(10014,1));
         this._news[this._news.length] = new MallMoneyVO(10015,1,new BaseRewardVO(10015,1));
         // 道具类物品
         this._news[this._news.length] = new MallMoneyVO(10020,1,new BaseRewardVO(10020,1));
         this._news[this._news.length] = new MallMoneyVO(10021,1,new BaseRewardVO(10021,1));
         this._news[this._news.length] = new MallMoneyVO(10022,1,new BaseRewardVO(10022,1));
         this._news[this._news.length] = new MallMoneyVO(10023,1,new BaseRewardVO(10023,1));
         this._news[this._news.length] = new MallMoneyVO(10024,1,new BaseRewardVO(10024,1));
         this._news[this._news.length] = new MallMoneyVO(10025,1,new BaseRewardVO(10025,1));
         this._news[this._news.length] = new MallMoneyVO(10026,1,new BaseRewardVO(10026,1));
         this._news[this._news.length] = new MallMoneyVO(10027,1,new BaseRewardVO(10027,1));
         this._news[this._news.length] = new MallMoneyVO(10028,1,new BaseRewardVO(10028,1));
         this._news[this._news.length] = new MallMoneyVO(10029,1,new BaseRewardVO(10029,1));
         this._news[this._news.length] = new MallMoneyVO(10030,1,new BaseRewardVO(10030,1));
         this._news[this._news.length] = new MallMoneyVO(10031,1,new BaseRewardVO(10031,1));
         this._news[this._news.length] = new MallMoneyVO(10032,1,new BaseRewardVO(10032,1));
         this._news[this._news.length] = new MallMoneyVO(10033,1,new BaseRewardVO(10033,1));
         this._news[this._news.length] = new MallMoneyVO(10034,1,new BaseRewardVO(10034,1));
         // 委任状类
         this._news[this._news.length] = new MallMoneyVO(10101,1,new BaseRewardVO(10101,1));
         this._news[this._news.length] = new MallMoneyVO(10102,1,new BaseRewardVO(10102,1));
         this._news[this._news.length] = new MallMoneyVO(10103,1,new BaseRewardVO(10103,1));
         // 亡魂类
         this._news[this._news.length] = new MallMoneyVO(10110,1,new BaseRewardVO(10110,1));
         this._news[this._news.length] = new MallMoneyVO(10111,1,new BaseRewardVO(10111,1));
         this._news[this._news.length] = new MallMoneyVO(10112,1,new BaseRewardVO(10112,1));
         this._news[this._news.length] = new MallMoneyVO(10113,1,new BaseRewardVO(10113,1));
         // 神兽装备材料
         this._news[this._news.length] = new MallMoneyVO(10151,1,new BaseRewardVO(10151,1));
         this._news[this._news.length] = new MallMoneyVO(10152,1,new BaseRewardVO(10152,1));
         this._news[this._news.length] = new MallMoneyVO(10153,1,new BaseRewardVO(10153,1));
         this._news[this._news.length] = new MallMoneyVO(10154,1,new BaseRewardVO(10154,1));
         this._news[this._news.length] = new MallMoneyVO(10155,1,new BaseRewardVO(10155,1));
         this._news[this._news.length] = new MallMoneyVO(10156,1,new BaseRewardVO(10156,1));
         this._news[this._news.length] = new MallMoneyVO(10157,1,new BaseRewardVO(10157,1));
         this._news[this._news.length] = new MallMoneyVO(10158,1,new BaseRewardVO(10158,1));
         this._news[this._news.length] = new MallMoneyVO(10161,1,new BaseRewardVO(10161,1));
         this._news[this._news.length] = new MallMoneyVO(10162,1,new BaseRewardVO(10162,1));
         this._news[this._news.length] = new MallMoneyVO(10163,1,new BaseRewardVO(10163,1));
         this._news[this._news.length] = new MallMoneyVO(10164,1,new BaseRewardVO(10164,1));
         this._news[this._news.length] = new MallMoneyVO(10165,1,new BaseRewardVO(10165,1));
         this._news[this._news.length] = new MallMoneyVO(10166,1,new BaseRewardVO(10166,1));
         this._news[this._news.length] = new MallMoneyVO(10167,1,new BaseRewardVO(10167,1));
         this._news[this._news.length] = new MallMoneyVO(10168,1,new BaseRewardVO(10168,1));
         // 配方类
         this._news[this._news.length] = new MallMoneyVO(10201,1,new BaseRewardVO(10201,1));
         this._news[this._news.length] = new MallMoneyVO(10202,1,new BaseRewardVO(10202,1));
         this._news[this._news.length] = new MallMoneyVO(10203,1,new BaseRewardVO(10203,1));
         this._news[this._news.length] = new MallMoneyVO(10204,1,new BaseRewardVO(10204,1));
         this._news[this._news.length] = new MallMoneyVO(10205,1,new BaseRewardVO(10205,1));
         this._news[this._news.length] = new MallMoneyVO(10206,1,new BaseRewardVO(10206,1));
         // 文君酒
         this._news[this._news.length] = new MallMoneyVO(10250,1,new BaseRewardVO(10250,1));
         // 升级丹系列
         this._news[this._news.length] = new MallMoneyVO(10271,1,new BaseRewardVO(10271,1));
         this._news[this._news.length] = new MallMoneyVO(10272,1,new BaseRewardVO(10272,1));
         this._news[this._news.length] = new MallMoneyVO(10273,1,new BaseRewardVO(10273,1));
         this._news[this._news.length] = new MallMoneyVO(10274,1,new BaseRewardVO(10274,1));
         this._news[this._news.length] = new MallMoneyVO(10275,1,new BaseRewardVO(10275,1));
         this._news[this._news.length] = new MallMoneyVO(10276,1,new BaseRewardVO(10276,1));
         this._news[this._news.length] = new MallMoneyVO(10277,1,new BaseRewardVO(10277,1));
         this._news[this._news.length] = new MallMoneyVO(10278,1,new BaseRewardVO(10278,1));
         this._news[this._news.length] = new MallMoneyVO(10279,1,new BaseRewardVO(10279,1));
         this._news[this._news.length] = new MallMoneyVO(10280,1,new BaseRewardVO(10280,1));
         this._news[this._news.length] = new MallMoneyVO(10281,1,new BaseRewardVO(10281,1));
         this._news[this._news.length] = new MallMoneyVO(10282,1,new BaseRewardVO(10282,1));
         this._news[this._news.length] = new MallMoneyVO(10283,1,new BaseRewardVO(10283,1));
         this._news[this._news.length] = new MallMoneyVO(10284,1,new BaseRewardVO(10284,1));
         this._news[this._news.length] = new MallMoneyVO(10285,1,new BaseRewardVO(10285,1));
         this._news[this._news.length] = new MallMoneyVO(10286,1,new BaseRewardVO(10286,1));
         this._news[this._news.length] = new MallMoneyVO(10287,1,new BaseRewardVO(10287,1));
         this._news[this._news.length] = new MallMoneyVO(10288,1,new BaseRewardVO(10288,1));
         this._news[this._news.length] = new MallMoneyVO(10289,1,new BaseRewardVO(10289,1));
         this._news[this._news.length] = new MallMoneyVO(10290,1,new BaseRewardVO(10290,1));
         this._news[this._news.length] = new MallMoneyVO(10291,1,new BaseRewardVO(10291,1));
         // 特殊道具
         this._news[this._news.length] = new MallMoneyVO(10300,1,new BaseRewardVO(10300,1));
         this._news[this._news.length] = new MallMoneyVO(10301,1,new BaseRewardVO(10301,1));
         this._news[this._news.length] = new MallMoneyVO(10302,1,new BaseRewardVO(10302,1));
         this._news[this._news.length] = new MallMoneyVO(10303,1,new BaseRewardVO(10303,1));
         this._news[this._news.length] = new MallMoneyVO(10304,1,new BaseRewardVO(10304,1));
         this._news[this._news.length] = new MallMoneyVO(10305,1,new BaseRewardVO(10305,1));
         this._news[this._news.length] = new MallMoneyVO(10306,1,new BaseRewardVO(10306,1));
         this._news[this._news.length] = new MallMoneyVO(10307,1,new BaseRewardVO(10307,1));
         this._news[this._news.length] = new MallMoneyVO(10308,1,new BaseRewardVO(10308,1));
         this._news[this._news.length] = new MallMoneyVO(10309,1,new BaseRewardVO(10309,1));
         this._news[this._news.length] = new MallMoneyVO(10310,1,new BaseRewardVO(10310,1));
         this._news[this._news.length] = new MallMoneyVO(10311,1,new BaseRewardVO(10311,1));
         this._news[this._news.length] = new MallMoneyVO(10312,1,new BaseRewardVO(10312,1));
         this._news[this._news.length] = new MallMoneyVO(10313,1,new BaseRewardVO(10313,1));
         this._news[this._news.length] = new MallMoneyVO(10314,1,new BaseRewardVO(10314,1));
         this._news[this._news.length] = new MallMoneyVO(10315,1,new BaseRewardVO(10315,1));
         this._news[this._news.length] = new MallMoneyVO(10316,1,new BaseRewardVO(10316,1));
         this._news[this._news.length] = new MallMoneyVO(10317,1,new BaseRewardVO(10317,1));
         this._news[this._news.length] = new MallMoneyVO(10320,1,new BaseRewardVO(10320,1));
         // 神兽蛋
         this._news[this._news.length] = new MallMoneyVO(10380,1,new BaseRewardVO(10380,1));
         this._news[this._news.length] = new MallMoneyVO(10381,1,new BaseRewardVO(10381,1));
         this._news[this._news.length] = new MallMoneyVO(10382,1,new BaseRewardVO(10382,1));
         this._news[this._news.length] = new MallMoneyVO(10383,1,new BaseRewardVO(10383,1));
         // 制作材料
         this._news[this._news.length] = new MallMoneyVO(10401,1,new BaseRewardVO(10401,1));
         this._news[this._news.length] = new MallMoneyVO(10402,1,new BaseRewardVO(10402,1));
         this._news[this._news.length] = new MallMoneyVO(10403,1,new BaseRewardVO(10403,1));
         this._news[this._news.length] = new MallMoneyVO(10404,1,new BaseRewardVO(10404,1));
         this._news[this._news.length] = new MallMoneyVO(10405,1,new BaseRewardVO(10405,1));
         this._news[this._news.length] = new MallMoneyVO(10406,1,new BaseRewardVO(10406,1));
         this._news[this._news.length] = new MallMoneyVO(10407,1,new BaseRewardVO(10407,1));
         this._news[this._news.length] = new MallMoneyVO(10408,1,new BaseRewardVO(10408,1));
         this._news[this._news.length] = new MallMoneyVO(10409,1,new BaseRewardVO(10409,1));
         this._news[this._news.length] = new MallMoneyVO(10410,1,new BaseRewardVO(10410,1));
         this._news[this._news.length] = new MallMoneyVO(10411,1,new BaseRewardVO(10411,1));
         this._news[this._news.length] = new MallMoneyVO(10412,1,new BaseRewardVO(10412,1));
         this._news[this._news.length] = new MallMoneyVO(10413,1,new BaseRewardVO(10413,1));
         this._news[this._news.length] = new MallMoneyVO(10414,1,new BaseRewardVO(10414,1));
         this._news[this._news.length] = new MallMoneyVO(10415,1,new BaseRewardVO(10415,1));
         this._news[this._news.length] = new MallMoneyVO(10416,1,new BaseRewardVO(10416,1));
         this._news[this._news.length] = new MallMoneyVO(10417,1,new BaseRewardVO(10417,1));
         this._news[this._news.length] = new MallMoneyVO(10418,1,new BaseRewardVO(10418,1));
         this._news[this._news.length] = new MallMoneyVO(10419,1,new BaseRewardVO(10419,1));
         this._news[this._news.length] = new MallMoneyVO(10420,1,new BaseRewardVO(10420,1));
         this._news[this._news.length] = new MallMoneyVO(10421,1,new BaseRewardVO(10421,1));
         this._news[this._news.length] = new MallMoneyVO(10422,1,new BaseRewardVO(10422,1));
         this._news[this._news.length] = new MallMoneyVO(10423,1,new BaseRewardVO(10423,1));
         // 神装材料
         this._news[this._news.length] = new MallMoneyVO(10450,1,new BaseRewardVO(10450,1));
         this._news[this._news.length] = new MallMoneyVO(10451,1,new BaseRewardVO(10451,1));
         this._news[this._news.length] = new MallMoneyVO(10452,1,new BaseRewardVO(10452,1));
         this._news[this._news.length] = new MallMoneyVO(10453,1,new BaseRewardVO(10453,1));
         this._news[this._news.length] = new MallMoneyVO(10454,1,new BaseRewardVO(10454,1));
         this._news[this._news.length] = new MallMoneyVO(10455,1,new BaseRewardVO(10455,1));
         this._news[this._news.length] = new MallMoneyVO(10456,1,new BaseRewardVO(10456,1));
         this._news[this._news.length] = new MallMoneyVO(10457,1,new BaseRewardVO(10457,1));
         this._news[this._news.length] = new MallMoneyVO(10458,1,new BaseRewardVO(10458,1));
         this._news[this._news.length] = new MallMoneyVO(10459,1,new BaseRewardVO(10459,1));
         this._news[this._news.length] = new MallMoneyVO(10460,1,new BaseRewardVO(10460,1));
         this._news[this._news.length] = new MallMoneyVO(10461,1,new BaseRewardVO(10461,1));
         this._news[this._news.length] = new MallMoneyVO(10462,1,new BaseRewardVO(10462,1));
         this._news[this._news.length] = new MallMoneyVO(10463,1,new BaseRewardVO(10463,1));
         this._news[this._news.length] = new MallMoneyVO(10464,1,new BaseRewardVO(10464,1));
         this._news[this._news.length] = new MallMoneyVO(10465,1,new BaseRewardVO(10465,1));
         // 至宝碎片
         this._news[this._news.length] = new MallMoneyVO(10466,1,new BaseRewardVO(10466,1));
         this._news[this._news.length] = new MallMoneyVO(10467,1,new BaseRewardVO(10467,1));
         this._news[this._news.length] = new MallMoneyVO(10468,1,new BaseRewardVO(10468,1));
         this._news[this._news.length] = new MallMoneyVO(10469,1,new BaseRewardVO(10469,1));
         // 至宝材料
         this._news[this._news.length] = new MallMoneyVO(10501,1,new BaseRewardVO(10501,1));
         this._news[this._news.length] = new MallMoneyVO(10502,1,new BaseRewardVO(10502,1));
         this._news[this._news.length] = new MallMoneyVO(10503,1,new BaseRewardVO(10503,1));
         this._news[this._news.length] = new MallMoneyVO(10504,1,new BaseRewardVO(10504,1));
         this._news[this._news.length] = new MallMoneyVO(10505,1,new BaseRewardVO(10505,1));
         this._news[this._news.length] = new MallMoneyVO(10506,1,new BaseRewardVO(10506,1));
         this._news[this._news.length] = new MallMoneyVO(10507,1,new BaseRewardVO(10507,1));
         this._news[this._news.length] = new MallMoneyVO(10508,1,new BaseRewardVO(10508,1));
         this._news[this._news.length] = new MallMoneyVO(10509,1,new BaseRewardVO(10509,1));
         this._news[this._news.length] = new MallMoneyVO(10510,1,new BaseRewardVO(10510,1));
         this._news[this._news.length] = new MallMoneyVO(10511,1,new BaseRewardVO(10511,1));
         this._news[this._news.length] = new MallMoneyVO(10512,1,new BaseRewardVO(10512,1));
         this._news[this._news.length] = new MallMoneyVO(10513,1,new BaseRewardVO(10513,1));
         this._news[this._news.length] = new MallMoneyVO(10514,1,new BaseRewardVO(10514,1));
         this._news[this._news.length] = new MallMoneyVO(10515,1,new BaseRewardVO(10515,1));
         this._news[this._news.length] = new MallMoneyVO(10516,1,new BaseRewardVO(10516,1));
         this._news[this._news.length] = new MallMoneyVO(10517,1,new BaseRewardVO(10517,1));
         this._news[this._news.length] = new MallMoneyVO(10518,1,new BaseRewardVO(10518,1));
         this._news[this._news.length] = new MallMoneyVO(10519,1,new BaseRewardVO(10519,1));
         this._news[this._news.length] = new MallMoneyVO(10521,1,new BaseRewardVO(10521,1));
         this._news[this._news.length] = new MallMoneyVO(10522,1,new BaseRewardVO(10522,1));
         this._news[this._news.length] = new MallMoneyVO(10523,1,new BaseRewardVO(10523,1));
         this._news[this._news.length] = new MallMoneyVO(10524,1,new BaseRewardVO(10524,1));
         this._news[this._news.length] = new MallMoneyVO(10525,1,new BaseRewardVO(10525,1));
         this._news[this._news.length] = new MallMoneyVO(10526,1,new BaseRewardVO(10526,1));
         this._news[this._news.length] = new MallMoneyVO(10527,1,new BaseRewardVO(10527,1));
         this._news[this._news.length] = new MallMoneyVO(10528,1,new BaseRewardVO(10528,1));
         this._news[this._news.length] = new MallMoneyVO(10530,1,new BaseRewardVO(10530,1));
         this._news[this._news.length] = new MallMoneyVO(10531,1,new BaseRewardVO(10531,1));
         this._news[this._news.length] = new MallMoneyVO(10532,1,new BaseRewardVO(10532,1));
         this._news[this._news.length] = new MallMoneyVO(10533,1,new BaseRewardVO(10533,1));
         this._news[this._news.length] = new MallMoneyVO(10534,1,new BaseRewardVO(10534,1));
         this._news[this._news.length] = new MallMoneyVO(10535,1,new BaseRewardVO(10535,1));
         this._news[this._news.length] = new MallMoneyVO(10536,1,new BaseRewardVO(10536,1));
         this._news[this._news.length] = new MallMoneyVO(10537,1,new BaseRewardVO(10537,1));
         this._news[this._news.length] = new MallMoneyVO(10538,1,new BaseRewardVO(10538,1));
         // 装备材料
         this._news[this._news.length] = new MallMoneyVO(10601,1,new BaseRewardVO(10601,1));
         this._news[this._news.length] = new MallMoneyVO(10602,1,new BaseRewardVO(10602,1));
         this._news[this._news.length] = new MallMoneyVO(10603,1,new BaseRewardVO(10603,1));
         this._news[this._news.length] = new MallMoneyVO(10604,1,new BaseRewardVO(10604,1));
         this._news[this._news.length] = new MallMoneyVO(10605,1,new BaseRewardVO(10605,1));
         this._news[this._news.length] = new MallMoneyVO(10606,1,new BaseRewardVO(10606,1));
         this._news[this._news.length] = new MallMoneyVO(10607,1,new BaseRewardVO(10607,1));
         this._news[this._news.length] = new MallMoneyVO(10608,1,new BaseRewardVO(10608,1));
         this._news[this._news.length] = new MallMoneyVO(10609,1,new BaseRewardVO(10609,1));
         this._news[this._news.length] = new MallMoneyVO(10610,1,new BaseRewardVO(10610,1));
         this._news[this._news.length] = new MallMoneyVO(10611,1,new BaseRewardVO(10611,1));
         this._news[this._news.length] = new MallMoneyVO(10612,1,new BaseRewardVO(10612,1));
         this._news[this._news.length] = new MallMoneyVO(10613,1,new BaseRewardVO(10613,1));
         this._news[this._news.length] = new MallMoneyVO(10614,1,new BaseRewardVO(10614,1));
         this._news[this._news.length] = new MallMoneyVO(10615,1,new BaseRewardVO(10615,1));
         this._news[this._news.length] = new MallMoneyVO(10616,1,new BaseRewardVO(10616,1));
         this._news[this._news.length] = new MallMoneyVO(10617,1,new BaseRewardVO(10617,1));
         this._news[this._news.length] = new MallMoneyVO(10618,1,new BaseRewardVO(10618,1));
         this._news[this._news.length] = new MallMoneyVO(10619,1,new BaseRewardVO(10619,1));
         this._news[this._news.length] = new MallMoneyVO(10620,1,new BaseRewardVO(10620,1));
         this._news[this._news.length] = new MallMoneyVO(10621,1,new BaseRewardVO(10621,1));
         this._news[this._news.length] = new MallMoneyVO(10622,1,new BaseRewardVO(10622,1));
         this._news[this._news.length] = new MallMoneyVO(10623,1,new BaseRewardVO(10623,1));
         this._news[this._news.length] = new MallMoneyVO(10624,1,new BaseRewardVO(10624,1));
         this._news[this._news.length] = new MallMoneyVO(10625,1,new BaseRewardVO(10625,1));
         this._news[this._news.length] = new MallMoneyVO(10626,1,new BaseRewardVO(10626,1));
         this._news[this._news.length] = new MallMoneyVO(10627,1,new BaseRewardVO(10627,1));
         this._news[this._news.length] = new MallMoneyVO(10628,1,new BaseRewardVO(10628,1));
         this._news[this._news.length] = new MallMoneyVO(10629,1,new BaseRewardVO(10629,1));
         this._news[this._news.length] = new MallMoneyVO(10630,1,new BaseRewardVO(10630,1));
         this._news[this._news.length] = new MallMoneyVO(10631,1,new BaseRewardVO(10631,1));
         this._news[this._news.length] = new MallMoneyVO(10632,1,new BaseRewardVO(10632,1));
         this._news[this._news.length] = new MallMoneyVO(10633,1,new BaseRewardVO(10633,1));
         this._news[this._news.length] = new MallMoneyVO(10634,1,new BaseRewardVO(10634,1));
         this._news[this._news.length] = new MallMoneyVO(10635,1,new BaseRewardVO(10635,1));
         this._news[this._news.length] = new MallMoneyVO(10636,1,new BaseRewardVO(10636,1));
         this._news[this._news.length] = new MallMoneyVO(10637,1,new BaseRewardVO(10637,1));
         this._news[this._news.length] = new MallMoneyVO(10638,1,new BaseRewardVO(10638,1));
         this._news[this._news.length] = new MallMoneyVO(10639,1,new BaseRewardVO(10639,1));
         this._news[this._news.length] = new MallMoneyVO(10640,1,new BaseRewardVO(10640,1));
         this._news[this._news.length] = new MallMoneyVO(10641,1,new BaseRewardVO(10641,1));
         this._news[this._news.length] = new MallMoneyVO(10642,1,new BaseRewardVO(10642,1));
         this._news[this._news.length] = new MallMoneyVO(10643,1,new BaseRewardVO(10643,1));
         this._news[this._news.length] = new MallMoneyVO(10644,1,new BaseRewardVO(10644,1));
         this._news[this._news.length] = new MallMoneyVO(10645,1,new BaseRewardVO(10645,1));
         this._news[this._news.length] = new MallMoneyVO(10646,1,new BaseRewardVO(10646,1));
         this._news[this._news.length] = new MallMoneyVO(10647,1,new BaseRewardVO(10647,1));
         this._news[this._news.length] = new MallMoneyVO(10648,1,new BaseRewardVO(10648,1));
         this._news[this._news.length] = new MallMoneyVO(10649,1,new BaseRewardVO(10649,1));
         this._news[this._news.length] = new MallMoneyVO(10650,1,new BaseRewardVO(10650,1));
         this._news[this._news.length] = new MallMoneyVO(10651,1,new BaseRewardVO(10651,1));
         this._news[this._news.length] = new MallMoneyVO(10652,1,new BaseRewardVO(10652,1));
         this._news[this._news.length] = new MallMoneyVO(10653,1,new BaseRewardVO(10653,1));
         this._news[this._news.length] = new MallMoneyVO(10654,1,new BaseRewardVO(10654,1));
         this._news[this._news.length] = new MallMoneyVO(10655,1,new BaseRewardVO(10655,1));
         this._news[this._news.length] = new MallMoneyVO(10656,1,new BaseRewardVO(10656,1));
         this._news[this._news.length] = new MallMoneyVO(10657,1,new BaseRewardVO(10657,1));
         this._news[this._news.length] = new MallMoneyVO(10658,1,new BaseRewardVO(10658,1));
         this._news[this._news.length] = new MallMoneyVO(10659,1,new BaseRewardVO(10659,1));
         this._news[this._news.length] = new MallMoneyVO(10660,1,new BaseRewardVO(10660,1));
         this._news[this._news.length] = new MallMoneyVO(10661,1,new BaseRewardVO(10661,1));
         // 转生材料
         this._news[this._news.length] = new MallMoneyVO(10701,1,new BaseRewardVO(10701,1));
         this._news[this._news.length] = new MallMoneyVO(10702,1,new BaseRewardVO(10702,1));
         this._news[this._news.length] = new MallMoneyVO(10703,1,new BaseRewardVO(10703,1));
         this._news[this._news.length] = new MallMoneyVO(10704,1,new BaseRewardVO(10704,1));
         this._news[this._news.length] = new MallMoneyVO(10705,1,new BaseRewardVO(10705,1));
         this._news[this._news.length] = new MallMoneyVO(10706,1,new BaseRewardVO(10706,1));
         this._news[this._news.length] = new MallMoneyVO(10707,1,new BaseRewardVO(10707,1));
         this._news[this._news.length] = new MallMoneyVO(10708,1,new BaseRewardVO(10708,1));
         this._news[this._news.length] = new MallMoneyVO(10709,1,new BaseRewardVO(10709,1));
         this._news[this._news.length] = new MallMoneyVO(10710,1,new BaseRewardVO(10710,1));
         this._news[this._news.length] = new MallMoneyVO(10711,1,new BaseRewardVO(10711,1));
         this._news[this._news.length] = new MallMoneyVO(10712,1,new BaseRewardVO(10712,1));
         this._news[this._news.length] = new MallMoneyVO(10713,1,new BaseRewardVO(10713,1));
         this._news[this._news.length] = new MallMoneyVO(10714,1,new BaseRewardVO(10714,1));
         this._news[this._news.length] = new MallMoneyVO(10715,1,new BaseRewardVO(10715,1));
         this._news[this._news.length] = new MallMoneyVO(10716,1,new BaseRewardVO(10716,1));
         this._news[this._news.length] = new MallMoneyVO(10717,1,new BaseRewardVO(10717,1));
         this._news[this._news.length] = new MallMoneyVO(10718,1,new BaseRewardVO(10718,1));
         // 主公技能卷轴
         this._news[this._news.length] = new MallMoneyVO(10801,1,new BaseRewardVO(10801,1));
         this._news[this._news.length] = new MallMoneyVO(10802,1,new BaseRewardVO(10802,1));
         this._news[this._news.length] = new MallMoneyVO(10806,1,new BaseRewardVO(10806,1));
         this._news[this._news.length] = new MallMoneyVO(10807,1,new BaseRewardVO(10807,1));
         this._news[this._news.length] = new MallMoneyVO(10811,1,new BaseRewardVO(10811,1));
         this._news[this._news.length] = new MallMoneyVO(10812,1,new BaseRewardVO(10812,1));
         this._news[this._news.length] = new MallMoneyVO(10816,1,new BaseRewardVO(10816,1));
         this._news[this._news.length] = new MallMoneyVO(10817,1,new BaseRewardVO(10817,1));
         this._news[this._news.length] = new MallMoneyVO(10821,1,new BaseRewardVO(10821,1));
         this._news[this._news.length] = new MallMoneyVO(10822,1,new BaseRewardVO(10822,1));
         // 源阶石
         this._news[this._news.length] = new MallMoneyVO(10831,1,new BaseRewardVO(10831,1));
         this._news[this._news.length] = new MallMoneyVO(10832,1,new BaseRewardVO(10832,1));
         this._news[this._news.length] = new MallMoneyVO(10833,1,new BaseRewardVO(10833,1));
         // 天石系列
         this._news[this._news.length] = new MallMoneyVO(10851,1,new BaseRewardVO(10851,1));
         this._news[this._news.length] = new MallMoneyVO(10852,1,new BaseRewardVO(10852,1));
         this._news[this._news.length] = new MallMoneyVO(10853,1,new BaseRewardVO(10853,1));
         this._news[this._news.length] = new MallMoneyVO(10854,1,new BaseRewardVO(10854,1));
         this._news[this._news.length] = new MallMoneyVO(10855,1,new BaseRewardVO(10855,1));
         this._news[this._news.length] = new MallMoneyVO(10856,1,new BaseRewardVO(10856,1));
         // 强化石
         this._news[this._news.length] = new MallMoneyVO(10901,1,new BaseRewardVO(10901,1));
         this._news[this._news.length] = new MallMoneyVO(10902,1,new BaseRewardVO(10902,1));
         this._news[this._news.length] = new MallMoneyVO(10903,1,new BaseRewardVO(10903,1));
         this._news[this._news.length] = new MallMoneyVO(10904,1,new BaseRewardVO(10904,1));
         this._news[this._news.length] = new MallMoneyVO(10905,1,new BaseRewardVO(10905,1));
         this._news[this._news.length] = new MallMoneyVO(10906,1,new BaseRewardVO(10906,1));
         this._news[this._news.length] = new MallMoneyVO(10907,1,new BaseRewardVO(10907,1));
         // 神兽技能石
         this._news[this._news.length] = new MallMoneyVO(10921,1,new BaseRewardVO(10921,1));
         this._news[this._news.length] = new MallMoneyVO(10922,1,new BaseRewardVO(10922,1));
         this._news[this._news.length] = new MallMoneyVO(10923,1,new BaseRewardVO(10923,1));
         this._news[this._news.length] = new MallMoneyVO(10924,1,new BaseRewardVO(10924,1));
         this._news[this._news.length] = new MallMoneyVO(10925,1,new BaseRewardVO(10925,1));
         this._news[this._news.length] = new MallMoneyVO(10926,1,new BaseRewardVO(10926,1));
         // 神化石
         this._news[this._news.length] = new MallMoneyVO(10931,1,new BaseRewardVO(10931,1));
         this._news[this._news.length] = new MallMoneyVO(10932,1,new BaseRewardVO(10932,1));
         this._news[this._news.length] = new MallMoneyVO(10933,1,new BaseRewardVO(10933,1));
         this._news[this._news.length] = new MallMoneyVO(10934,1,new BaseRewardVO(10934,1));
         this._news[this._news.length] = new MallMoneyVO(10935,1,new BaseRewardVO(10935,1));
         this._news[this._news.length] = new MallMoneyVO(10936,1,new BaseRewardVO(10936,1));
         this._news[this._news.length] = new MallMoneyVO(10937,1,new BaseRewardVO(10937,1));
         // 技能石
         this._news[this._news.length] = new MallMoneyVO(10951,1,new BaseRewardVO(10951,1));
         this._news[this._news.length] = new MallMoneyVO(10952,1,new BaseRewardVO(10952,1));
         this._news[this._news.length] = new MallMoneyVO(10953,1,new BaseRewardVO(10953,1));
         this._news[this._news.length] = new MallMoneyVO(10954,1,new BaseRewardVO(10954,1));
         this._news[this._news.length] = new MallMoneyVO(10955,1,new BaseRewardVO(10955,1));
         this._news[this._news.length] = new MallMoneyVO(10956,1,new BaseRewardVO(10956,1));
         this._news[this._news.length] = new MallMoneyVO(10957,1,new BaseRewardVO(10957,1));
         this._news[this._news.length] = new MallMoneyVO(10958,1,new BaseRewardVO(10958,1));
         // 特殊道具
         this._news[this._news.length] = new MallMoneyVO(10980,1,new BaseRewardVO(10980,1));
         this._news[this._news.length] = new MallMoneyVO(10981,1,new BaseRewardVO(10981,1));
         this._news[this._news.length] = new MallMoneyVO(10982,1,new BaseRewardVO(10982,1));
         this._news[this._news.length] = new MallMoneyVO(10983,1,new BaseRewardVO(10983,1));
         this._news[this._news.length] = new MallMoneyVO(10984,1,new BaseRewardVO(10984,1));
         this._news[this._news.length] = new MallMoneyVO(10985,1,new BaseRewardVO(10985,1));
         this._news[this._news.length] = new MallMoneyVO(10986,1,new BaseRewardVO(10986,1));
         this._news[this._news.length] = new MallMoneyVO(10987,1,new BaseRewardVO(10987,1));
         // 卷轴类
         this._news[this._news.length] = new MallMoneyVO(11001,1,new BaseRewardVO(11001,1));
         this._news[this._news.length] = new MallMoneyVO(11002,1,new BaseRewardVO(11002,1));
         this._news[this._news.length] = new MallMoneyVO(11003,1,new BaseRewardVO(11003,1));
         this._news[this._news.length] = new MallMoneyVO(11004,1,new BaseRewardVO(11004,1));
         this._news[this._news.length] = new MallMoneyVO(11005,1,new BaseRewardVO(11005,1));
         this._news[this._news.length] = new MallMoneyVO(11007,1,new BaseRewardVO(11007,1));
         this._news[this._news.length] = new MallMoneyVO(11008,1,new BaseRewardVO(11008,1));
         this._news[this._news.length] = new MallMoneyVO(11011,1,new BaseRewardVO(11011,1));
         this._news[this._news.length] = new MallMoneyVO(11012,1,new BaseRewardVO(11012,1));
         this._news[this._news.length] = new MallMoneyVO(11013,1,new BaseRewardVO(11013,1));
         this._news[this._news.length] = new MallMoneyVO(11071,1,new BaseRewardVO(11071,1));
         this._news[this._news.length] = new MallMoneyVO(11072,1,new BaseRewardVO(11072,1));
         this._news[this._news.length] = new MallMoneyVO(11073,1,new BaseRewardVO(11073,1));
         this._news[this._news.length] = new MallMoneyVO(11101,1,new BaseRewardVO(11101,1));
         this._news[this._news.length] = new MallMoneyVO(11104,1,new BaseRewardVO(11104,1));
         this._news[this._news.length] = new MallMoneyVO(11106,1,new BaseRewardVO(11106,1));
         this._news[this._news.length] = new MallMoneyVO(11151,1,new BaseRewardVO(11151,1));
         this._news[this._news.length] = new MallMoneyVO(11153,1,new BaseRewardVO(11153,1));
         this._news[this._news.length] = new MallMoneyVO(11154,1,new BaseRewardVO(11154,1));
         this._news[this._news.length] = new MallMoneyVO(11157,1,new BaseRewardVO(11157,1));
         this._news[this._news.length] = new MallMoneyVO(11158,1,new BaseRewardVO(11158,1));
         this._news[this._news.length] = new MallMoneyVO(11201,1,new BaseRewardVO(11201,1));
         this._news[this._news.length] = new MallMoneyVO(11204,1,new BaseRewardVO(11204,1));
         this._news[this._news.length] = new MallMoneyVO(11205,1,new BaseRewardVO(11205,1));
         this._news[this._news.length] = new MallMoneyVO(11301,1,new BaseRewardVO(11301,1));
         this._news[this._news.length] = new MallMoneyVO(11302,1,new BaseRewardVO(11302,1));
         this._news[this._news.length] = new MallMoneyVO(11401,1,new BaseRewardVO(11401,1));
         this._news[this._news.length] = new MallMoneyVO(11501,1,new BaseRewardVO(11501,1));
         this._news[this._news.length] = new MallMoneyVO(11502,1,new BaseRewardVO(11502,1));
         this._news[this._news.length] = new MallMoneyVO(11503,1,new BaseRewardVO(11503,1));
         this._news[this._news.length] = new MallMoneyVO(11601,1,new BaseRewardVO(11601,1));
         this._news[this._news.length] = new MallMoneyVO(11602,1,new BaseRewardVO(11602,1));
         // 装备图纸 (12000系列)
         this._news[this._news.length] = new MallMoneyVO(12001,1,new BaseRewardVO(12001,1));
         this._news[this._news.length] = new MallMoneyVO(12002,1,new BaseRewardVO(12002,1));
         this._news[this._news.length] = new MallMoneyVO(12003,1,new BaseRewardVO(12003,1));
         this._news[this._news.length] = new MallMoneyVO(12004,1,new BaseRewardVO(12004,1));
         this._news[this._news.length] = new MallMoneyVO(12005,1,new BaseRewardVO(12005,1));
         this._news[this._news.length] = new MallMoneyVO(12006,1,new BaseRewardVO(12006,1));
         this._news[this._news.length] = new MallMoneyVO(12007,1,new BaseRewardVO(12007,1));
         this._news[this._news.length] = new MallMoneyVO(12008,1,new BaseRewardVO(12008,1));
         this._news[this._news.length] = new MallMoneyVO(12009,1,new BaseRewardVO(12009,1));
         this._news[this._news.length] = new MallMoneyVO(12010,1,new BaseRewardVO(12010,1));
         this._news[this._news.length] = new MallMoneyVO(12011,1,new BaseRewardVO(12011,1));
         this._news[this._news.length] = new MallMoneyVO(12012,1,new BaseRewardVO(12012,1));
         this._news[this._news.length] = new MallMoneyVO(12013,1,new BaseRewardVO(12013,1));
         this._news[this._news.length] = new MallMoneyVO(12014,1,new BaseRewardVO(12014,1));
         this._news[this._news.length] = new MallMoneyVO(12015,1,new BaseRewardVO(12015,1));
         this._news[this._news.length] = new MallMoneyVO(12016,1,new BaseRewardVO(12016,1));
         this._news[this._news.length] = new MallMoneyVO(12017,1,new BaseRewardVO(12017,1));
         this._news[this._news.length] = new MallMoneyVO(12018,1,new BaseRewardVO(12018,1));
         this._news[this._news.length] = new MallMoneyVO(12019,1,new BaseRewardVO(12019,1));
         this._news[this._news.length] = new MallMoneyVO(12020,1,new BaseRewardVO(12020,1));
         this._news[this._news.length] = new MallMoneyVO(12021,1,new BaseRewardVO(12021,1));
         this._news[this._news.length] = new MallMoneyVO(12022,1,new BaseRewardVO(12022,1));
         this._news[this._news.length] = new MallMoneyVO(12023,1,new BaseRewardVO(12023,1));
         this._news[this._news.length] = new MallMoneyVO(12024,1,new BaseRewardVO(12024,1));
         this._news[this._news.length] = new MallMoneyVO(12025,1,new BaseRewardVO(12025,1));
         this._news[this._news.length] = new MallMoneyVO(12031,1,new BaseRewardVO(12031,1));
         this._news[this._news.length] = new MallMoneyVO(12032,1,new BaseRewardVO(12032,1));
         this._news[this._news.length] = new MallMoneyVO(12033,1,new BaseRewardVO(12033,1));
         this._news[this._news.length] = new MallMoneyVO(12034,1,new BaseRewardVO(12034,1));
         this._news[this._news.length] = new MallMoneyVO(12035,1,new BaseRewardVO(12035,1));
         this._news[this._news.length] = new MallMoneyVO(12036,1,new BaseRewardVO(12036,1));
         this._news[this._news.length] = new MallMoneyVO(12037,1,new BaseRewardVO(12037,1));
         this._news[this._news.length] = new MallMoneyVO(12038,1,new BaseRewardVO(12038,1));
         this._news[this._news.length] = new MallMoneyVO(12039,1,new BaseRewardVO(12039,1));
         this._news[this._news.length] = new MallMoneyVO(12040,1,new BaseRewardVO(12040,1));
         this._news[this._news.length] = new MallMoneyVO(12041,1,new BaseRewardVO(12041,1));
         this._news[this._news.length] = new MallMoneyVO(12042,1,new BaseRewardVO(12042,1));
         this._news[this._news.length] = new MallMoneyVO(12043,1,new BaseRewardVO(12043,1));
         this._news[this._news.length] = new MallMoneyVO(12044,1,new BaseRewardVO(12044,1));
         this._news[this._news.length] = new MallMoneyVO(12045,1,new BaseRewardVO(12045,1));
         // 武将信物 (50000系列)
         this._news[this._news.length] = new MallMoneyVO(50001,1,new BaseRewardVO(50001,1));
         this._news[this._news.length] = new MallMoneyVO(50002,1,new BaseRewardVO(50002,1));
         this._news[this._news.length] = new MallMoneyVO(50003,1,new BaseRewardVO(50003,1));
         this._news[this._news.length] = new MallMoneyVO(50004,1,new BaseRewardVO(50004,1));
         this._news[this._news.length] = new MallMoneyVO(50005,1,new BaseRewardVO(50005,1));
         this._news[this._news.length] = new MallMoneyVO(50006,1,new BaseRewardVO(50006,1));
         this._news[this._news.length] = new MallMoneyVO(50007,1,new BaseRewardVO(50007,1));
         this._news[this._news.length] = new MallMoneyVO(50009,1,new BaseRewardVO(50009,1));
         this._news[this._news.length] = new MallMoneyVO(50010,1,new BaseRewardVO(50010,1));
         this._news[this._news.length] = new MallMoneyVO(50011,1,new BaseRewardVO(50011,1));
         this._news[this._news.length] = new MallMoneyVO(50012,1,new BaseRewardVO(50012,1));
         this._news[this._news.length] = new MallMoneyVO(50013,1,new BaseRewardVO(50013,1));
         this._news[this._news.length] = new MallMoneyVO(50014,1,new BaseRewardVO(50014,1));
         this._news[this._news.length] = new MallMoneyVO(50015,1,new BaseRewardVO(50015,1));
         this._news[this._news.length] = new MallMoneyVO(50016,1,new BaseRewardVO(50016,1));
         this._news[this._news.length] = new MallMoneyVO(50017,1,new BaseRewardVO(50017,1));
         this._news[this._news.length] = new MallMoneyVO(50018,1,new BaseRewardVO(50018,1));
         this._news[this._news.length] = new MallMoneyVO(50019,1,new BaseRewardVO(50019,1));
         this._news[this._news.length] = new MallMoneyVO(50020,1,new BaseRewardVO(50020,1));
         this._news[this._news.length] = new MallMoneyVO(50021,1,new BaseRewardVO(50021,1));
         this._news[this._news.length] = new MallMoneyVO(50022,1,new BaseRewardVO(50022,1));
         this._news[this._news.length] = new MallMoneyVO(50023,1,new BaseRewardVO(50023,1));
         this._news[this._news.length] = new MallMoneyVO(50024,1,new BaseRewardVO(50024,1));
         this._news[this._news.length] = new MallMoneyVO(50025,1,new BaseRewardVO(50025,1));
         this._news[this._news.length] = new MallMoneyVO(50026,1,new BaseRewardVO(50026,1));
         this._news[this._news.length] = new MallMoneyVO(50027,1,new BaseRewardVO(50027,1));
         this._news[this._news.length] = new MallMoneyVO(50028,1,new BaseRewardVO(50028,1));
         this._news[this._news.length] = new MallMoneyVO(50029,1,new BaseRewardVO(50029,1));
         this._news[this._news.length] = new MallMoneyVO(50030,1,new BaseRewardVO(50030,1));
         this._news[this._news.length] = new MallMoneyVO(50031,1,new BaseRewardVO(50031,1));
         this._news[this._news.length] = new MallMoneyVO(50032,1,new BaseRewardVO(50032,1));
         this._news[this._news.length] = new MallMoneyVO(50033,1,new BaseRewardVO(50033,1));
         this._news[this._news.length] = new MallMoneyVO(50034,1,new BaseRewardVO(50034,1));
         this._news[this._news.length] = new MallMoneyVO(50035,1,new BaseRewardVO(50035,1));
         this._news[this._news.length] = new MallMoneyVO(50036,1,new BaseRewardVO(50036,1));
         this._news[this._news.length] = new MallMoneyVO(50037,1,new BaseRewardVO(50037,1));
         this._news[this._news.length] = new MallMoneyVO(50038,1,new BaseRewardVO(50038,1));
         this._news[this._news.length] = new MallMoneyVO(50039,1,new BaseRewardVO(50039,1));
         this._news[this._news.length] = new MallMoneyVO(50040,1,new BaseRewardVO(50040,1));
         this._news[this._news.length] = new MallMoneyVO(50041,1,new BaseRewardVO(50041,1));
         this._news[this._news.length] = new MallMoneyVO(50042,1,new BaseRewardVO(50042,1));
         this._news[this._news.length] = new MallMoneyVO(50043,1,new BaseRewardVO(50043,1));
         this._news[this._news.length] = new MallMoneyVO(50044,1,new BaseRewardVO(50044,1));
         this._news[this._news.length] = new MallMoneyVO(50045,1,new BaseRewardVO(50045,1));
         this._news[this._news.length] = new MallMoneyVO(50046,1,new BaseRewardVO(50046,1));
         this._news[this._news.length] = new MallMoneyVO(50047,1,new BaseRewardVO(50047,1));
         this._news[this._news.length] = new MallMoneyVO(50048,1,new BaseRewardVO(50048,1));
         // 卡片碎片 (68000系列)
         this._news[this._news.length] = new MallMoneyVO(68001,1,new BaseRewardVO(68001,1));
         this._news[this._news.length] = new MallMoneyVO(68002,1,new BaseRewardVO(68002,1));
         // 特殊物品 (70000系列)
         this._news[this._news.length] = new MallMoneyVO(70001,1,new BaseRewardVO(70001,1));
         this._news[this._news.length] = new MallMoneyVO(70002,1,new BaseRewardVO(70002,1));
         this._news[this._news.length] = new MallMoneyVO(70003,1,new BaseRewardVO(70003,1));
         this._news[this._news.length] = new MallMoneyVO(70004,1,new BaseRewardVO(70004,1));
         // 神兽装备 (80000系列)
         this._news[this._news.length] = new MallMoneyVO(80001,1,new BaseRewardVO(80001,1));
         this._news[this._news.length] = new MallMoneyVO(80002,1,new BaseRewardVO(80002,1));
         this._news[this._news.length] = new MallMoneyVO(80003,1,new BaseRewardVO(80003,1));
         this._news[this._news.length] = new MallMoneyVO(80101,1,new BaseRewardVO(80101,1));
         this._news[this._news.length] = new MallMoneyVO(80102,1,new BaseRewardVO(80102,1));
         this._news[this._news.length] = new MallMoneyVO(80103,1,new BaseRewardVO(80103,1));
         this._news[this._news.length] = new MallMoneyVO(80201,1,new BaseRewardVO(80201,1));
         this._news[this._news.length] = new MallMoneyVO(80202,1,new BaseRewardVO(80202,1));
         this._news[this._news.length] = new MallMoneyVO(80203,1,new BaseRewardVO(80203,1));
         this._news[this._news.length] = new MallMoneyVO(80301,1,new BaseRewardVO(80301,1));
         this._news[this._news.length] = new MallMoneyVO(80302,1,new BaseRewardVO(80302,1));
         this._news[this._news.length] = new MallMoneyVO(80303,1,new BaseRewardVO(80303,1));
         this._news[this._news.length] = new MallMoneyVO(80401,1,new BaseRewardVO(80401,1));
         this._news[this._news.length] = new MallMoneyVO(80402,1,new BaseRewardVO(80402,1));
         this._news[this._news.length] = new MallMoneyVO(80403,1,new BaseRewardVO(80403,1));
         // 1星神兽装备 (81000系列)
         this._news[this._news.length] = new MallMoneyVO(81001,1,new BaseRewardVO(81001,1));
         this._news[this._news.length] = new MallMoneyVO(81002,1,new BaseRewardVO(81002,1));
         this._news[this._news.length] = new MallMoneyVO(81003,1,new BaseRewardVO(81003,1));
         this._news[this._news.length] = new MallMoneyVO(81101,1,new BaseRewardVO(81101,1));
         this._news[this._news.length] = new MallMoneyVO(81102,1,new BaseRewardVO(81102,1));
         this._news[this._news.length] = new MallMoneyVO(81103,1,new BaseRewardVO(81103,1));
         this._news[this._news.length] = new MallMoneyVO(81201,1,new BaseRewardVO(81201,1));
         this._news[this._news.length] = new MallMoneyVO(81202,1,new BaseRewardVO(81202,1));
         this._news[this._news.length] = new MallMoneyVO(81203,1,new BaseRewardVO(81203,1));
         this._news[this._news.length] = new MallMoneyVO(81301,1,new BaseRewardVO(81301,1));
         this._news[this._news.length] = new MallMoneyVO(81302,1,new BaseRewardVO(81302,1));
         this._news[this._news.length] = new MallMoneyVO(81303,1,new BaseRewardVO(81303,1));
         this._news[this._news.length] = new MallMoneyVO(81401,1,new BaseRewardVO(81401,1));
         this._news[this._news.length] = new MallMoneyVO(81402,1,new BaseRewardVO(81402,1));
         this._news[this._news.length] = new MallMoneyVO(81403,1,new BaseRewardVO(81403,1));
         this._news[this._news.length] = new MallMoneyVO(81501,1,new BaseRewardVO(81501,1));
         this._news[this._news.length] = new MallMoneyVO(81502,1,new BaseRewardVO(81502,1));
         this._news[this._news.length] = new MallMoneyVO(81503,1,new BaseRewardVO(81503,1));
         // 2星神兽装备 (82000系列)
         this._news[this._news.length] = new MallMoneyVO(82001,1,new BaseRewardVO(82001,1));
         this._news[this._news.length] = new MallMoneyVO(82002,1,new BaseRewardVO(82002,1));
         this._news[this._news.length] = new MallMoneyVO(82003,1,new BaseRewardVO(82003,1));
         this._news[this._news.length] = new MallMoneyVO(82101,1,new BaseRewardVO(82101,1));
         this._news[this._news.length] = new MallMoneyVO(82102,1,new BaseRewardVO(82102,1));
         this._news[this._news.length] = new MallMoneyVO(82103,1,new BaseRewardVO(82103,1));
         this._news[this._news.length] = new MallMoneyVO(82201,1,new BaseRewardVO(82201,1));
         this._news[this._news.length] = new MallMoneyVO(82202,1,new BaseRewardVO(82202,1));
         this._news[this._news.length] = new MallMoneyVO(82203,1,new BaseRewardVO(82203,1));
         this._news[this._news.length] = new MallMoneyVO(82301,1,new BaseRewardVO(82301,1));
         this._news[this._news.length] = new MallMoneyVO(82302,1,new BaseRewardVO(82302,1));
         this._news[this._news.length] = new MallMoneyVO(82303,1,new BaseRewardVO(82303,1));
         this._news[this._news.length] = new MallMoneyVO(82401,1,new BaseRewardVO(82401,1));
         this._news[this._news.length] = new MallMoneyVO(82402,1,new BaseRewardVO(82402,1));
         this._news[this._news.length] = new MallMoneyVO(82403,1,new BaseRewardVO(82403,1));
         this._news[this._news.length] = new MallMoneyVO(82501,1,new BaseRewardVO(82501,1));
         this._news[this._news.length] = new MallMoneyVO(82502,1,new BaseRewardVO(82502,1));
         this._news[this._news.length] = new MallMoneyVO(82503,1,new BaseRewardVO(82503,1));
         // 3星神兽装备 (83000系列)
         this._news[this._news.length] = new MallMoneyVO(83001,1,new BaseRewardVO(83001,1));
         this._news[this._news.length] = new MallMoneyVO(83002,1,new BaseRewardVO(83002,1));
         this._news[this._news.length] = new MallMoneyVO(83003,1,new BaseRewardVO(83003,1));
         this._news[this._news.length] = new MallMoneyVO(83101,1,new BaseRewardVO(83101,1));
         this._news[this._news.length] = new MallMoneyVO(83102,1,new BaseRewardVO(83102,1));
         this._news[this._news.length] = new MallMoneyVO(83103,1,new BaseRewardVO(83103,1));
         this._news[this._news.length] = new MallMoneyVO(83201,1,new BaseRewardVO(83201,1));
         this._news[this._news.length] = new MallMoneyVO(83202,1,new BaseRewardVO(83202,1));
         this._news[this._news.length] = new MallMoneyVO(83203,1,new BaseRewardVO(83203,1));
         this._news[this._news.length] = new MallMoneyVO(83301,1,new BaseRewardVO(83301,1));
         this._news[this._news.length] = new MallMoneyVO(83302,1,new BaseRewardVO(83302,1));
         this._news[this._news.length] = new MallMoneyVO(83303,1,new BaseRewardVO(83303,1));
         this._news[this._news.length] = new MallMoneyVO(83401,1,new BaseRewardVO(83401,1));
         this._news[this._news.length] = new MallMoneyVO(83402,1,new BaseRewardVO(83402,1));
         this._news[this._news.length] = new MallMoneyVO(83403,1,new BaseRewardVO(83403,1));
         this._news[this._news.length] = new MallMoneyVO(83501,1,new BaseRewardVO(83501,1));
         this._news[this._news.length] = new MallMoneyVO(83502,1,new BaseRewardVO(83502,1));
         this._news[this._news.length] = new MallMoneyVO(83503,1,new BaseRewardVO(83503,1));
         // 4星神兽装备 (84000系列)
         this._news[this._news.length] = new MallMoneyVO(84001,1,new BaseRewardVO(84001,1));
         this._news[this._news.length] = new MallMoneyVO(84002,1,new BaseRewardVO(84002,1));
         this._news[this._news.length] = new MallMoneyVO(84003,1,new BaseRewardVO(84003,1));
         this._news[this._news.length] = new MallMoneyVO(84101,1,new BaseRewardVO(84101,1));
         this._news[this._news.length] = new MallMoneyVO(84102,1,new BaseRewardVO(84102,1));
         this._news[this._news.length] = new MallMoneyVO(84103,1,new BaseRewardVO(84103,1));
         this._news[this._news.length] = new MallMoneyVO(84201,1,new BaseRewardVO(84201,1));
         this._news[this._news.length] = new MallMoneyVO(84202,1,new BaseRewardVO(84202,1));
         this._news[this._news.length] = new MallMoneyVO(84203,1,new BaseRewardVO(84203,1));
         this._news[this._news.length] = new MallMoneyVO(84301,1,new BaseRewardVO(84301,1));
         this._news[this._news.length] = new MallMoneyVO(84302,1,new BaseRewardVO(84302,1));
         this._news[this._news.length] = new MallMoneyVO(84303,1,new BaseRewardVO(84303,1));
         this._news[this._news.length] = new MallMoneyVO(84401,1,new BaseRewardVO(84401,1));
         this._news[this._news.length] = new MallMoneyVO(84402,1,new BaseRewardVO(84402,1));
         this._news[this._news.length] = new MallMoneyVO(84403,1,new BaseRewardVO(84403,1));
         this._news[this._news.length] = new MallMoneyVO(84501,1,new BaseRewardVO(84501,1));
         this._news[this._news.length] = new MallMoneyVO(84502,1,new BaseRewardVO(84502,1));
         this._news[this._news.length] = new MallMoneyVO(84503,1,new BaseRewardVO(84503,1));
         // 5星神兽装备 (85000系列)
         this._news[this._news.length] = new MallMoneyVO(85001,1,new BaseRewardVO(85001,1));
         this._news[this._news.length] = new MallMoneyVO(85002,1,new BaseRewardVO(85002,1));
         this._news[this._news.length] = new MallMoneyVO(85003,1,new BaseRewardVO(85003,1));
         this._news[this._news.length] = new MallMoneyVO(85101,1,new BaseRewardVO(85101,1));
         this._news[this._news.length] = new MallMoneyVO(85102,1,new BaseRewardVO(85102,1));
         this._news[this._news.length] = new MallMoneyVO(85103,1,new BaseRewardVO(85103,1));
         this._news[this._news.length] = new MallMoneyVO(85201,1,new BaseRewardVO(85201,1));
         this._news[this._news.length] = new MallMoneyVO(85202,1,new BaseRewardVO(85202,1));
         this._news[this._news.length] = new MallMoneyVO(85203,1,new BaseRewardVO(85203,1));
         this._news[this._news.length] = new MallMoneyVO(85301,1,new BaseRewardVO(85301,1));
         this._news[this._news.length] = new MallMoneyVO(85302,1,new BaseRewardVO(85302,1));
         this._news[this._news.length] = new MallMoneyVO(85303,1,new BaseRewardVO(85303,1));
         this._news[this._news.length] = new MallMoneyVO(85401,1,new BaseRewardVO(85401,1));
         this._news[this._news.length] = new MallMoneyVO(85402,1,new BaseRewardVO(85402,1));
         this._news[this._news.length] = new MallMoneyVO(85403,1,new BaseRewardVO(85403,1));
         this._news[this._news.length] = new MallMoneyVO(85501,1,new BaseRewardVO(85501,1));
         this._news[this._news.length] = new MallMoneyVO(85502,1,new BaseRewardVO(85502,1));
         this._news[this._news.length] = new MallMoneyVO(85503,1,new BaseRewardVO(85503,1));
         // 1星神装 (89000系列)
         this._news[this._news.length] = new MallMoneyVO(89001,1,new BaseRewardVO(89001,1));
         this._news[this._news.length] = new MallMoneyVO(89002,1,new BaseRewardVO(89002,1));
         this._news[this._news.length] = new MallMoneyVO(89003,1,new BaseRewardVO(89003,1));
         this._news[this._news.length] = new MallMoneyVO(89101,1,new BaseRewardVO(89101,1));
         this._news[this._news.length] = new MallMoneyVO(89102,1,new BaseRewardVO(89102,1));
         this._news[this._news.length] = new MallMoneyVO(89103,1,new BaseRewardVO(89103,1));
         this._news[this._news.length] = new MallMoneyVO(89201,1,new BaseRewardVO(89201,1));
         this._news[this._news.length] = new MallMoneyVO(89202,1,new BaseRewardVO(89202,1));
         this._news[this._news.length] = new MallMoneyVO(89203,1,new BaseRewardVO(89203,1));
         this._news[this._news.length] = new MallMoneyVO(89301,1,new BaseRewardVO(89301,1));
         this._news[this._news.length] = new MallMoneyVO(89302,1,new BaseRewardVO(89302,1));
         this._news[this._news.length] = new MallMoneyVO(89303,1,new BaseRewardVO(89303,1));
         this._news[this._news.length] = new MallMoneyVO(89401,1,new BaseRewardVO(89401,1));
         this._news[this._news.length] = new MallMoneyVO(89402,1,new BaseRewardVO(89402,1));
         this._news[this._news.length] = new MallMoneyVO(89403,1,new BaseRewardVO(89403,1));
         this._news[this._news.length] = new MallMoneyVO(89501,1,new BaseRewardVO(89501,1));
         this._news[this._news.length] = new MallMoneyVO(89502,1,new BaseRewardVO(89502,1));
         this._news[this._news.length] = new MallMoneyVO(89503,1,new BaseRewardVO(89503,1));
         // 2星神装 (89011系列)
         this._news[this._news.length] = new MallMoneyVO(89011,1,new BaseRewardVO(89011,1));
         this._news[this._news.length] = new MallMoneyVO(89012,1,new BaseRewardVO(89012,1));
         this._news[this._news.length] = new MallMoneyVO(89013,1,new BaseRewardVO(89013,1));
         this._news[this._news.length] = new MallMoneyVO(89111,1,new BaseRewardVO(89111,1));
         this._news[this._news.length] = new MallMoneyVO(89112,1,new BaseRewardVO(89112,1));
         this._news[this._news.length] = new MallMoneyVO(89113,1,new BaseRewardVO(89113,1));
         this._news[this._news.length] = new MallMoneyVO(89211,1,new BaseRewardVO(89211,1));
         this._news[this._news.length] = new MallMoneyVO(89212,1,new BaseRewardVO(89212,1));
         this._news[this._news.length] = new MallMoneyVO(89213,1,new BaseRewardVO(89213,1));
         this._news[this._news.length] = new MallMoneyVO(89311,1,new BaseRewardVO(89311,1));
         this._news[this._news.length] = new MallMoneyVO(89312,1,new BaseRewardVO(89312,1));
         this._news[this._news.length] = new MallMoneyVO(89313,1,new BaseRewardVO(89313,1));
         this._news[this._news.length] = new MallMoneyVO(89411,1,new BaseRewardVO(89411,1));
         this._news[this._news.length] = new MallMoneyVO(89412,1,new BaseRewardVO(89412,1));
         this._news[this._news.length] = new MallMoneyVO(89413,1,new BaseRewardVO(89413,1));
         this._news[this._news.length] = new MallMoneyVO(89511,1,new BaseRewardVO(89511,1));
         this._news[this._news.length] = new MallMoneyVO(89512,1,new BaseRewardVO(89512,1));
         this._news[this._news.length] = new MallMoneyVO(89513,1,new BaseRewardVO(89513,1));
         // 3星神装 (89021系列)
         this._news[this._news.length] = new MallMoneyVO(89021,1,new BaseRewardVO(89021,1));
         this._news[this._news.length] = new MallMoneyVO(89022,1,new BaseRewardVO(89022,1));
         this._news[this._news.length] = new MallMoneyVO(89023,1,new BaseRewardVO(89023,1));
         this._news[this._news.length] = new MallMoneyVO(89121,1,new BaseRewardVO(89121,1));
         this._news[this._news.length] = new MallMoneyVO(89122,1,new BaseRewardVO(89122,1));
         this._news[this._news.length] = new MallMoneyVO(89123,1,new BaseRewardVO(89123,1));
         this._news[this._news.length] = new MallMoneyVO(89221,1,new BaseRewardVO(89221,1));
         this._news[this._news.length] = new MallMoneyVO(89222,1,new BaseRewardVO(89222,1));
         this._news[this._news.length] = new MallMoneyVO(89223,1,new BaseRewardVO(89223,1));
         this._news[this._news.length] = new MallMoneyVO(89321,1,new BaseRewardVO(89321,1));
         this._news[this._news.length] = new MallMoneyVO(89322,1,new BaseRewardVO(89322,1));
         this._news[this._news.length] = new MallMoneyVO(89323,1,new BaseRewardVO(89323,1));
         this._news[this._news.length] = new MallMoneyVO(89421,1,new BaseRewardVO(89421,1));
         this._news[this._news.length] = new MallMoneyVO(89422,1,new BaseRewardVO(89422,1));
         this._news[this._news.length] = new MallMoneyVO(89423,1,new BaseRewardVO(89423,1));
         this._news[this._news.length] = new MallMoneyVO(89521,1,new BaseRewardVO(89521,1));
         this._news[this._news.length] = new MallMoneyVO(89522,1,new BaseRewardVO(89522,1));
         this._news[this._news.length] = new MallMoneyVO(89523,1,new BaseRewardVO(89523,1));
         // 装备类 (20000-49999系列) - 添加主要装备ID
         this._news[this._news.length] = new MallMoneyVO(20001,1,new BaseRewardVO(20001,1));
         this._news[this._news.length] = new MallMoneyVO(20002,1,new BaseRewardVO(20002,1));
         this._news[this._news.length] = new MallMoneyVO(20003,1,new BaseRewardVO(20003,1));
         this._news[this._news.length] = new MallMoneyVO(20004,1,new BaseRewardVO(20004,1));
         this._news[this._news.length] = new MallMoneyVO(20005,1,new BaseRewardVO(20005,1));
         this._news[this._news.length] = new MallMoneyVO(20006,1,new BaseRewardVO(20006,1));
         this._news[this._news.length] = new MallMoneyVO(20007,1,new BaseRewardVO(20007,1));
         this._news[this._news.length] = new MallMoneyVO(20008,1,new BaseRewardVO(20008,1));
         this._news[this._news.length] = new MallMoneyVO(20009,1,new BaseRewardVO(20009,1));
         this._news[this._news.length] = new MallMoneyVO(20010,1,new BaseRewardVO(20010,1));
         // 皮肤类 (40000+系列) - 添加主要皮肤ID
         this._news[this._news.length] = new MallMoneyVO(40001,1,new BaseRewardVO(40001,1));
         this._news[this._news.length] = new MallMoneyVO(40002,1,new BaseRewardVO(40002,1));
         this._news[this._news.length] = new MallMoneyVO(40003,1,new BaseRewardVO(40003,1));
         this._news[this._news.length] = new MallMoneyVO(40004,1,new BaseRewardVO(40004,1));
         this._news[this._news.length] = new MallMoneyVO(40005,1,new BaseRewardVO(40005,1));
         this._news[this._news.length] = new MallMoneyVO(40006,1,new BaseRewardVO(40006,1));
         this._news[this._news.length] = new MallMoneyVO(40007,1,new BaseRewardVO(40007,1));
         this._news[this._news.length] = new MallMoneyVO(40008,1,new BaseRewardVO(40008,1));
         this._news[this._news.length] = new MallMoneyVO(40009,1,new BaseRewardVO(40009,1));
         this._news[this._news.length] = new MallMoneyVO(40010,1,new BaseRewardVO(40010,1));
         // 春节皮肤系列
         this._news[this._news.length] = new MallMoneyVO(49990,1,new BaseRewardVO(49990,1));
         this._news[this._news.length] = new MallMoneyVO(49991,1,new BaseRewardVO(49991,1));
         this._news[this._news.length] = new MallMoneyVO(49992,1,new BaseRewardVO(49992,1));
         this._news[this._news.length] = new MallMoneyVO(49993,1,new BaseRewardVO(49993,1));
         this._news[this._news.length] = new MallMoneyVO(49994,1,new BaseRewardVO(49994,1));
         // 圣诞皮肤系列
         this._news[this._news.length] = new MallMoneyVO(49995,1,new BaseRewardVO(49995,1));
         this._news[this._news.length] = new MallMoneyVO(49996,1,new BaseRewardVO(49996,1));
         this._news[this._news.length] = new MallMoneyVO(49997,1,new BaseRewardVO(49997,1));
         this._news[this._news.length] = new MallMoneyVO(49998,1,new BaseRewardVO(49998,1));
         this._news[this._news.length] = new MallMoneyVO(49999,1,new BaseRewardVO(49999,1));

         // 只添加真实存在的物品ID - 基于物品ID列表.txt中的实际物品
         // 添加更多至宝材料 (实际存在的ID)
         this._news[this._news.length] = new MallMoneyVO(10539,1,new BaseRewardVO(10539,1));
         this._news[this._news.length] = new MallMoneyVO(10540,1,new BaseRewardVO(10540,1));
         this._news[this._news.length] = new MallMoneyVO(10541,1,new BaseRewardVO(10541,1));
         this._news[this._news.length] = new MallMoneyVO(10542,1,new BaseRewardVO(10542,1));
         this._news[this._news.length] = new MallMoneyVO(10543,1,new BaseRewardVO(10543,1));
         this._news[this._news.length] = new MallMoneyVO(10544,1,new BaseRewardVO(10544,1));
         this._news[this._news.length] = new MallMoneyVO(10545,1,new BaseRewardVO(10545,1));
         this._news[this._news.length] = new MallMoneyVO(10546,1,new BaseRewardVO(10546,1));
         this._news[this._news.length] = new MallMoneyVO(10547,1,new BaseRewardVO(10547,1));
         this._news[this._news.length] = new MallMoneyVO(10548,1,new BaseRewardVO(10548,1));
         this._news[this._news.length] = new MallMoneyVO(10549,1,new BaseRewardVO(10549,1));
         this._news[this._news.length] = new MallMoneyVO(10550,1,new BaseRewardVO(10550,1));
         this._news[this._news.length] = new MallMoneyVO(10551,1,new BaseRewardVO(10551,1));
         this._news[this._news.length] = new MallMoneyVO(10552,1,new BaseRewardVO(10552,1));
         this._news[this._news.length] = new MallMoneyVO(10553,1,new BaseRewardVO(10553,1));
         this._news[this._news.length] = new MallMoneyVO(10554,1,new BaseRewardVO(10554,1));
         this._news[this._news.length] = new MallMoneyVO(10555,1,new BaseRewardVO(10555,1));
         this._news[this._news.length] = new MallMoneyVO(10556,1,new BaseRewardVO(10556,1));
         this._news[this._news.length] = new MallMoneyVO(10557,1,new BaseRewardVO(10557,1));
         this._news[this._news.length] = new MallMoneyVO(10558,1,new BaseRewardVO(10558,1));
         this._news[this._news.length] = new MallMoneyVO(10559,1,new BaseRewardVO(10559,1));
         this._news[this._news.length] = new MallMoneyVO(10560,1,new BaseRewardVO(10560,1));
         this._news[this._news.length] = new MallMoneyVO(10561,1,new BaseRewardVO(10561,1));
         this._news[this._news.length] = new MallMoneyVO(10562,1,new BaseRewardVO(10562,1));
         this._news[this._news.length] = new MallMoneyVO(10563,1,new BaseRewardVO(10563,1));
         this._news[this._news.length] = new MallMoneyVO(10564,1,new BaseRewardVO(10564,1));
         this._news[this._news.length] = new MallMoneyVO(10565,1,new BaseRewardVO(10565,1));
         this._news[this._news.length] = new MallMoneyVO(10566,1,new BaseRewardVO(10566,1));
         this._news[this._news.length] = new MallMoneyVO(10567,1,new BaseRewardVO(10567,1));
         this._news[this._news.length] = new MallMoneyVO(10568,1,new BaseRewardVO(10568,1));
         this._news[this._news.length] = new MallMoneyVO(10569,1,new BaseRewardVO(10569,1));
         this._news[this._news.length] = new MallMoneyVO(10570,1,new BaseRewardVO(10570,1));
         this._news[this._news.length] = new MallMoneyVO(10571,1,new BaseRewardVO(10571,1));
         this._news[this._news.length] = new MallMoneyVO(10572,1,new BaseRewardVO(10572,1));
         this._news[this._news.length] = new MallMoneyVO(10573,1,new BaseRewardVO(10573,1));
         this._news[this._news.length] = new MallMoneyVO(10574,1,new BaseRewardVO(10574,1));
         this._news[this._news.length] = new MallMoneyVO(10575,1,new BaseRewardVO(10575,1));
         this._news[this._news.length] = new MallMoneyVO(10576,1,new BaseRewardVO(10576,1));
         this._news[this._news.length] = new MallMoneyVO(10577,1,new BaseRewardVO(10577,1));
         this._news[this._news.length] = new MallMoneyVO(10578,1,new BaseRewardVO(10578,1));
         this._news[this._news.length] = new MallMoneyVO(10579,1,new BaseRewardVO(10579,1));
         this._news[this._news.length] = new MallMoneyVO(10580,1,new BaseRewardVO(10580,1));
         this._news[this._news.length] = new MallMoneyVO(10581,1,new BaseRewardVO(10581,1));
         this._news[this._news.length] = new MallMoneyVO(10582,1,new BaseRewardVO(10582,1));
         this._news[this._news.length] = new MallMoneyVO(10583,1,new BaseRewardVO(10583,1));
         this._news[this._news.length] = new MallMoneyVO(10584,1,new BaseRewardVO(10584,1));
         this._news[this._news.length] = new MallMoneyVO(10585,1,new BaseRewardVO(10585,1));
         this._news[this._news.length] = new MallMoneyVO(10586,1,new BaseRewardVO(10586,1));
         this._news[this._news.length] = new MallMoneyVO(10587,1,new BaseRewardVO(10587,1));
         this._news[this._news.length] = new MallMoneyVO(10588,1,new BaseRewardVO(10588,1));
         this._news[this._news.length] = new MallMoneyVO(10589,1,new BaseRewardVO(10589,1));
         this._news[this._news.length] = new MallMoneyVO(10590,1,new BaseRewardVO(10590,1));
         this._news[this._news.length] = new MallMoneyVO(10591,1,new BaseRewardVO(10591,1));
         this._news[this._news.length] = new MallMoneyVO(10592,1,new BaseRewardVO(10592,1));
         this._news[this._news.length] = new MallMoneyVO(10593,1,new BaseRewardVO(10593,1));
         this._news[this._news.length] = new MallMoneyVO(10594,1,new BaseRewardVO(10594,1));
         this._news[this._news.length] = new MallMoneyVO(10595,1,new BaseRewardVO(10595,1));
         this._news[this._news.length] = new MallMoneyVO(10596,1,new BaseRewardVO(10596,1));
         this._news[this._news.length] = new MallMoneyVO(10597,1,new BaseRewardVO(10597,1));
         this._news[this._news.length] = new MallMoneyVO(10598,1,new BaseRewardVO(10598,1));
         this._news[this._news.length] = new MallMoneyVO(10599,1,new BaseRewardVO(10599,1));
         this._news[this._news.length] = new MallMoneyVO(10600,1,new BaseRewardVO(10600,1));
         this._news[this._news.length] = new MallMoneyVO(10659,1,new BaseRewardVO(10600,1));
         this._news[this._news.length] = new MallMoneyVO(10671,1,new BaseRewardVO(10671,1));
         this._news[this._news.length] = new MallMoneyVO(10672,1,new BaseRewardVO(10672,1));
         this._news[this._news.length] = new MallMoneyVO(10673,1,new BaseRewardVO(10673,1));
         this._news[this._news.length] = new MallMoneyVO(10674,1,new BaseRewardVO(10674,1));
         this._news[this._news.length] = new MallMoneyVO(10675,1,new BaseRewardVO(10675,1));
         this._news[this._news.length] = new MallMoneyVO(10676,1,new BaseRewardVO(10676,1));
         this._news[this._news.length] = new MallMoneyVO(10677,1,new BaseRewardVO(10677,1));
         this._news[this._news.length] = new MallMoneyVO(10678,1,new BaseRewardVO(10678,1));
         this._news[this._news.length] = new MallMoneyVO(10679,1,new BaseRewardVO(10679,1));
         this._news[this._news.length] = new MallMoneyVO(10680,1,new BaseRewardVO(10680,1));
         this._news[this._news.length] = new MallMoneyVO(10681,1,new BaseRewardVO(10681,1));
         this._news[this._news.length] = new MallMoneyVO(10682,1,new BaseRewardVO(10682,1));
         this._news[this._news.length] = new MallMoneyVO(10683,1,new BaseRewardVO(10683,1));
         this._news[this._news.length] = new MallMoneyVO(10684,1,new BaseRewardVO(10684,1));
         this._news[this._news.length] = new MallMoneyVO(10685,1,new BaseRewardVO(10685,1));
         this._news[this._news.length] = new MallMoneyVO(10686,1,new BaseRewardVO(10686,1));
         this._news[this._news.length] = new MallMoneyVO(10687,1,new BaseRewardVO(10687,1));
         this._news[this._news.length] = new MallMoneyVO(10688,1,new BaseRewardVO(10688,1));
         this._news[this._news.length] = new MallMoneyVO(10689,1,new BaseRewardVO(10689,1));
         this._news[this._news.length] = new MallMoneyVO(10690,1,new BaseRewardVO(10690,1));
         this._news[this._news.length] = new MallMoneyVO(10691,1,new BaseRewardVO(10691,1));
         this._news[this._news.length] = new MallMoneyVO(10692,1,new BaseRewardVO(10692,1));
         this._news[this._news.length] = new MallMoneyVO(10693,1,new BaseRewardVO(10693,1));
         this._news[this._news.length] = new MallMoneyVO(10694,1,new BaseRewardVO(10694,1));
         this._news[this._news.length] = new MallMoneyVO(10695,1,new BaseRewardVO(10695,1));
         this._news[this._news.length] = new MallMoneyVO(10696,1,new BaseRewardVO(10696,1));
         this._news[this._news.length] = new MallMoneyVO(10697,1,new BaseRewardVO(10697,1));
         this._news[this._news.length] = new MallMoneyVO(10698,1,new BaseRewardVO(10698,1));
         this._news[this._news.length] = new MallMoneyVO(10699,1,new BaseRewardVO(10699,1));
         this._news[this._news.length] = new MallMoneyVO(10700,1,new BaseRewardVO(10700,1));


         // 添加42000系列皮肤 (从物品列表中提取的实际皮肤ID)
         this._news[this._news.length] = new MallMoneyVO(42001,1,new BaseRewardVO(42001,1));
         this._news[this._news.length] = new MallMoneyVO(42002,1,new BaseRewardVO(42002,1));
         this._news[this._news.length] = new MallMoneyVO(42003,1,new BaseRewardVO(42003,1));
         this._news[this._news.length] = new MallMoneyVO(42004,1,new BaseRewardVO(42004,1));
         this._news[this._news.length] = new MallMoneyVO(42005,1,new BaseRewardVO(42005,1));
         this._news[this._news.length] = new MallMoneyVO(42006,1,new BaseRewardVO(42006,1));
         this._news[this._news.length] = new MallMoneyVO(42011,1,new BaseRewardVO(42011,1));
         this._news[this._news.length] = new MallMoneyVO(42012,1,new BaseRewardVO(42012,1));
         this._news[this._news.length] = new MallMoneyVO(42013,1,new BaseRewardVO(42013,1));
         this._news[this._news.length] = new MallMoneyVO(42014,1,new BaseRewardVO(42014,1));
         this._news[this._news.length] = new MallMoneyVO(42015,1,new BaseRewardVO(42015,1));
         this._news[this._news.length] = new MallMoneyVO(42016,1,new BaseRewardVO(42016,1));
         this._news[this._news.length] = new MallMoneyVO(42017,1,new BaseRewardVO(42017,1));
         this._news[this._news.length] = new MallMoneyVO(42021,1,new BaseRewardVO(42021,1));
         this._news[this._news.length] = new MallMoneyVO(42022,1,new BaseRewardVO(42022,1));
         this._news[this._news.length] = new MallMoneyVO(42023,1,new BaseRewardVO(42023,1));
         this._news[this._news.length] = new MallMoneyVO(42024,1,new BaseRewardVO(42024,1));
         this._news[this._news.length] = new MallMoneyVO(42025,1,new BaseRewardVO(42025,1));
         this._news[this._news.length] = new MallMoneyVO(42026,1,new BaseRewardVO(42026,1));
         this._news[this._news.length] = new MallMoneyVO(42027,1,new BaseRewardVO(42027,1));
         this._news[this._news.length] = new MallMoneyVO(42031,1,new BaseRewardVO(42031,1));
         this._news[this._news.length] = new MallMoneyVO(42032,1,new BaseRewardVO(42032,1));
         this._news[this._news.length] = new MallMoneyVO(42033,1,new BaseRewardVO(42033,1));
         this._news[this._news.length] = new MallMoneyVO(42034,1,new BaseRewardVO(42034,1));
         this._news[this._news.length] = new MallMoneyVO(42035,1,new BaseRewardVO(42035,1));
         this._news[this._news.length] = new MallMoneyVO(42036,1,new BaseRewardVO(42036,1));
         this._news[this._news.length] = new MallMoneyVO(42037,1,new BaseRewardVO(42037,1));
         this._news[this._news.length] = new MallMoneyVO(42041,1,new BaseRewardVO(42041,1));
         this._news[this._news.length] = new MallMoneyVO(42042,1,new BaseRewardVO(42042,1));
         this._news[this._news.length] = new MallMoneyVO(42043,1,new BaseRewardVO(42043,1));
         this._news[this._news.length] = new MallMoneyVO(42044,1,new BaseRewardVO(42044,1));
         this._news[this._news.length] = new MallMoneyVO(42045,1,new BaseRewardVO(42045,1));
         this._news[this._news.length] = new MallMoneyVO(42046,1,new BaseRewardVO(42046,1));
         this._news[this._news.length] = new MallMoneyVO(42047,1,new BaseRewardVO(42047,1));
         this._news[this._news.length] = new MallMoneyVO(42051,1,new BaseRewardVO(42051,1));
         this._news[this._news.length] = new MallMoneyVO(42052,1,new BaseRewardVO(42052,1));
         this._news[this._news.length] = new MallMoneyVO(42053,1,new BaseRewardVO(42053,1));
         this._news[this._news.length] = new MallMoneyVO(42054,1,new BaseRewardVO(42054,1));
         this._news[this._news.length] = new MallMoneyVO(42055,1,new BaseRewardVO(42055,1));
         this._news[this._news.length] = new MallMoneyVO(42056,1,new BaseRewardVO(42056,1));
         this._news[this._news.length] = new MallMoneyVO(42057,1,new BaseRewardVO(42057,1));

         this._goods = [];
         this._goods[this._goods.length] = new MallMoneyVO(3392,8,new BaseRewardVO(10300,1));
         this._goods[this._goods.length] = new MallMoneyVO(3578,20,new BaseRewardVO(10303,1));
         this._goods[this._goods.length] = new MallMoneyVO(3577,20,new BaseRewardVO(11401,1));
         this._goods[this._goods.length] = new MallMoneyVO(3951,30,new BaseRewardVO(10280,1));
         this._goods[this._goods.length] = new MallMoneyVO(3825,27,new BaseRewardVO(10279,1));
         this._goods[this._goods.length] = new MallMoneyVO(3776,25,new BaseRewardVO(10278,1));
         this._goods[this._goods.length] = new MallMoneyVO(3725,23,new BaseRewardVO(10277,1));
         this._goods[this._goods.length] = new MallMoneyVO(3656,20,new BaseRewardVO(10276,1));
         this._goods[this._goods.length] = new MallMoneyVO(3600,17,new BaseRewardVO(10275,1));
         this._goods[this._goods.length] = new MallMoneyVO(3508,14,new BaseRewardVO(10274,1));
         this._goods[this._goods.length] = new MallMoneyVO(3493,11,new BaseRewardVO(10273,1));
         this._goods[this._goods.length] = new MallMoneyVO(3421,8,new BaseRewardVO(10272,1));
         this._goods[this._goods.length] = new MallMoneyVO(3420,5,new BaseRewardVO(10271,1));
         this._goods[this._goods.length] = new MallMoneyVO(3608,5,new BaseRewardVO(10306,1));
         this._goods[this._goods.length] = new MallMoneyVO(3575,2,new BaseRewardVO(10304,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,12,new BaseRewardVO(50036,1));
         this._goods[this._goods.length] = new MallMoneyVO(3739,12,new BaseRewardVO(50028,1));
         this._goods[this._goods.length] = new MallMoneyVO(3692,12,new BaseRewardVO(50022,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,12,new BaseRewardVO(50035,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,12,new BaseRewardVO(50032,1));
         this._goods[this._goods.length] = new MallMoneyVO(3691,12,new BaseRewardVO(50018,1));
         this._goods[this._goods.length] = new MallMoneyVO(3729,12,new BaseRewardVO(50025,1));
         this._goods[this._goods.length] = new MallMoneyVO(3646,12,new BaseRewardVO(50016,1));
         this._goods[this._goods.length] = new MallMoneyVO(3626,12,new BaseRewardVO(50015,1));
         this._goods[this._goods.length] = new MallMoneyVO(3558,12,new BaseRewardVO(50004,1));
         this._goods[this._goods.length] = new MallMoneyVO(3599,12,new BaseRewardVO(50011,1));
         this._goods[this._goods.length] = new MallMoneyVO(3557,12,new BaseRewardVO(50005,1));
         this._goods[this._goods.length] = new MallMoneyVO(3736,12,new BaseRewardVO(50026,1));
         this._goods[this._goods.length] = new MallMoneyVO(3585,12,new BaseRewardVO(50001,1));
         this._goods[this._goods.length] = new MallMoneyVO(3448,12,new BaseRewardVO(50003,1));
         this._goods[this._goods.length] = new MallMoneyVO(3447,12,new BaseRewardVO(50002,1));
         this._goods[this._goods.length] = new MallMoneyVO(3592,12,new BaseRewardVO(50010,1));
         this._goods[this._goods.length] = new MallMoneyVO(3780,12,new BaseRewardVO(50029,1));
         this._goods[this._goods.length] = new MallMoneyVO(3393,30,new BaseRewardVO(10020,1));
         this._goods[this._goods.length] = new MallMoneyVO(3512,9,new BaseRewardVO(10980,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,5,new BaseRewardVO(10533,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,5,new BaseRewardVO(10534,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,5,new BaseRewardVO(10535,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10521,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10522,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10523,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10524,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10525,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,5,new BaseRewardVO(10526,1));
         this._goods[this._goods.length] = new MallMoneyVO(3394,3,new BaseRewardVO(10301,1));
         this._goods[this._goods.length] = new MallMoneyVO(3395,9,new BaseRewardVO(10302,1));
         this._goods[this._goods.length] = new MallMoneyVO(3396,3,new BaseRewardVO(10250,1));
         this._goods[this._goods.length] = new MallMoneyVO(3597,14,new BaseRewardVO(10907,1));
         this._goods[this._goods.length] = new MallMoneyVO(3576,12,new BaseRewardVO(10906,1));
         this._goods[this._goods.length] = new MallMoneyVO(3506,10,new BaseRewardVO(10905,1));
         this._goods[this._goods.length] = new MallMoneyVO(3491,8,new BaseRewardVO(10904,1));
         this._goods[this._goods.length] = new MallMoneyVO(3398,6,new BaseRewardVO(10903,1));
         this._goods[this._goods.length] = new MallMoneyVO(3399,4,new BaseRewardVO(10902,1));
         this._goods[this._goods.length] = new MallMoneyVO(3400,2,new BaseRewardVO(10901,1));
         this._goods[this._goods.length] = new MallMoneyVO(3737,16,new BaseRewardVO(10958,1));
         this._goods[this._goods.length] = new MallMoneyVO(3690,14,new BaseRewardVO(10957,1));
         this._goods[this._goods.length] = new MallMoneyVO(3598,12,new BaseRewardVO(10956,1));
         this._goods[this._goods.length] = new MallMoneyVO(3507,10,new BaseRewardVO(10955,1));
         this._goods[this._goods.length] = new MallMoneyVO(3401,8,new BaseRewardVO(10954,1));
         this._goods[this._goods.length] = new MallMoneyVO(3402,6,new BaseRewardVO(10953,1));
         this._goods[this._goods.length] = new MallMoneyVO(3403,4,new BaseRewardVO(10952,1));
         this._goods[this._goods.length] = new MallMoneyVO(3404,2,new BaseRewardVO(10951,1));
         this._goods[this._goods.length] = new MallMoneyVO(3405,15,new BaseRewardVO(10103,1));
         this._goods[this._goods.length] = new MallMoneyVO(3406,8,new BaseRewardVO(10102,1));
         this._goods[this._goods.length] = new MallMoneyVO(3407,3,new BaseRewardVO(10101,1));
         this._bags = [];
         this._bags[this._bags.length] = new MallMoneyVO(3382,2,new BaseRewardVO(11071,1));
         this._bags[this._bags.length] = new MallMoneyVO(3383,5,new BaseRewardVO(11072,1));
         this._bags[this._bags.length] = new MallMoneyVO(3384,5,new BaseRewardVO(11073,1));
         this._bags[this._bags.length] = new MallMoneyVO(3720,4,new BaseRewardVO(11012,1));
         this._bags[this._bags.length] = new MallMoneyVO(3721,7,new BaseRewardVO(11158,1));
         this._bags[this._bags.length] = new MallMoneyVO(3722,7,new BaseRewardVO(11205,1));
         this._bags[this._bags.length] = new MallMoneyVO(3499,3,new BaseRewardVO(11003,1));
         this._bags[this._bags.length] = new MallMoneyVO(3498,2,new BaseRewardVO(11302,1));
         this._bags[this._bags.length] = new MallMoneyVO(3723,3,new BaseRewardVO(11501,1));
         this._bags[this._bags.length] = new MallMoneyVO(3381,3,new BaseRewardVO(11011,1));
         this._bags[this._bags.length] = new MallMoneyVO(3386,5,new BaseRewardVO(11151,1));
         this._bags[this._bags.length] = new MallMoneyVO(3388,5,new BaseRewardVO(11157,1));
         this._bags[this._bags.length] = new MallMoneyVO(3390,5,new BaseRewardVO(11204,1));
         this._bags[this._bags.length] = new MallMoneyVO(3389,4,new BaseRewardVO(11201,1));
         this._bags[this._bags.length] = new MallMoneyVO(3387,3,new BaseRewardVO(11154,1));
         this._bags[this._bags.length] = new MallMoneyVO(3391,1,new BaseRewardVO(11301,1));
         this._bags[this._bags.length] = new MallMoneyVO(3379,3,new BaseRewardVO(11005,1));
         this._bags[this._bags.length] = new MallMoneyVO(3378,2,new BaseRewardVO(11002,1));
         this._bags[this._bags.length] = new MallMoneyVO(3380,3,new BaseRewardVO(11008,1));
         this._bags[this._bags.length] = new MallMoneyVO(3385,3,new BaseRewardVO(11104,1));
         this._skins = [];
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42541,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42531,3));
         this._skins[this._skins.length] = new MallMoneyVO(3584,66,new EquipRewardVO(42101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42471,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42351,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42361,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42381,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42371,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42241,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42251,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42511,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42461,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42451,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42431,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42411,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42441,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42261,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42211,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42221,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42421,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42231,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42311,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42321,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42391,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42271,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42291,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42331,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42341,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42131,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42141,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42171,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42191,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42181,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42151,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42281,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42161,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41801,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42001,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42111,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(42121,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41701,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40501,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41901,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40601,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40701,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40901,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41001,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(40801,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41501,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,158,new EquipRewardVO(41601,3));
      }
      
      public function findMall(param1:int) : Array
      {
         switch(param1)
         {
            case 0:
               return this._news;
            case 1:
               return this._goods;
            case 2:
               return this._bags;
            case 3:
               return this._skins;
            default:
               return [];
         }
      }
      
      public function findMallVO(param1:int) : MallMoneyVO
      {
         var _loc3_:MallMoneyVO = null;
         var _loc2_:Array = this._news.concat(this._goods,this._bags,this._skins);
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.rewardVO.constGood.id == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function hasFashion(param1:int) : Boolean
      {
         var _loc2_:ConstFashionVO = null;
         var _loc3_:MallMoneyVO = null;
         return true;
      }
   }
}

