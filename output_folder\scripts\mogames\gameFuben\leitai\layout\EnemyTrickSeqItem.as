package mogames.gameFuben.leitai.layout
{
   import com.mogames.display.MovieClipUI;
   import com.mogames.utils.FontUtil;
   import mogames.ConstData;
   
   public class EnemyTrickSeqItem extends MovieClipUI
   {
      
      public function EnemyTrickSeqItem()
      {
         super("ENEMY_TRICK_SEQ_ITEM");
      }
      
      public function initData(param1:Object) : void
      {
         if(param1.hide)
         {
            FontUtil.setText(ui.txt,"?");
         }
         else
         {
            FontUtil.setText(ui.txt,ConstData.TRICKS[param1.tid]);
         }
      }
   }
}

